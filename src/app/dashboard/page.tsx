
"use client"

import { UsersModal } from "@/components/_components/users-modal"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Users } from "lucide-react"
import { useState } from "react"

export default function Page() {
  const [isUsersModalOpen, setIsUsersModalOpen] = useState(false)

  return (
    <div className="p-6">
      <div className="flex flex-col gap-4">
        <h1 className="text-2xl font-bold">Dashboard</h1>
        <div className="flex gap-4">
          <Button
            onClick={() => setIsUsersModalOpen(true)}
            className="flex items-center gap-2"
          >
            <Users className="h-4 w-4" />
            View Users
          </Button>
        </div>
      </div>

      <UsersModal
        open={isUsersModalOpen}
        onOpenChange={setIsUsersModalOpen}
      />
    </div>
  )
}
